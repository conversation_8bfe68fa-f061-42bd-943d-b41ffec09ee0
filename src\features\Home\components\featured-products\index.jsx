import product1 from "../../../../assets/images/factoryImg.jpg";
import product2 from "../../../../assets/images/factoryImg.jpg";
import product3 from "../../../../assets/images/factoryImg.jpg";
import product4 from "../../../../assets/images/factoryImg.jpg";
import bgImage from "../../../../assets/images/factoryImg.jpg";
import Carousel from "../../../../components/Carousel";

const products = [
  {
    image: product1,
    title: "Rotary Dryer",
    desc: "Rotary Dryer is also called Single drum Dryer or Single Cylinder Dryer, which is a kind of directly heated continuous rotating cylinder drying equipment....",
  },
  {
    image: product2,
    title: "Heavy Hammer Crushers",
    desc: "The heavy hammer crusher is commonly used for crushing materials with compressive strength....",
  },
  {
    image: product3,
    title: "Ball Mill",
    desc: "Ball mill is a common grinding equipment, but also an important core equipment in mineral processing operations. ....",
  },
  {
    image: product4,
    title: "Box Crushers",
    desc: "Box crusher aka Square box crusher has the characteristics of simple structure, easy maintenance, low price and high cost performance....",
  },
  {
    image: product1,
    title: "Cone Crushers",
    desc: "Cone crushers are compression machines that take in raw materials and reduce them in size by crushing them between a moving piece of steel and a stationary piece of steel....",
  },
  {
    image: product2,
    title: "Vibrating Screen",
    desc: "Vibrating screens are used to separate materials into various sizes for further processing or for end use. Depending on your needs....",
  },
  {
    image: product3,
    title: "Sand Washing Machine",
    desc: "Sand washing machine is used to remove the dust in sand. It aims at improving the quality of sand. The materials include the required gravel in building-site....",
  },
  {
    image: product4,
    title: "Jaw Crushers",
    desc: "Jaw crushers are typically used as primary crushers, or the first step in the process of reducing rock. They typically crush using compression....",
  },
];

export default function FeaturedProducts() {
  const renderProductCard = (product, index, itemsPerSlide) => {
    if (itemsPerSlide === 1) {
      // Single card layout - center and constrain width on small screens
      return (
        <div className="flex justify-center">
          <div
            key={index}
            className="bg-white text-black rounded-xl shadow-md overflow-hidden hover:shadow-lg transition-all duration-300 w-full max-w-sm mx-auto"
          >
            <img
              src={product.image}
              alt={product.title}
              className="w-full h-40 sm:h-44 md:h-48 object-cover"
            />
            <div className="p-4 sm:p-5 md:p-6 bg-white">
              <h4 className="font-semibold text-base sm:text-lg md:text-xl mb-2 sm:mb-3 min-h-[3rem] sm:min-h-[3.5rem] md:min-h-[4rem] line-clamp-2">
                {product.title}
              </h4>
              <p className="text-xs sm:text-sm md:text-base text-gray-600 leading-relaxed min-h-[4rem] sm:min-h-[4.5rem] md:min-h-[5rem] line-clamp-3">
                {product.desc}
              </p>
              <a
                href="#"
                className="text-xs sm:text-sm md:text-base text-[var(--primary)] font-medium mt-3 sm:mt-4 inline-block hover:underline transition-colors duration-200"
              >
                Read More
              </a>
            </div>
          </div>
        </div>
      );
    }

    // Multi-card layout - normal grid behavior
    return (
      <div
        key={index}
        className="bg-white text-black rounded-xl shadow-md overflow-hidden hover:shadow-lg transition-all duration-300"
      >
        <img
          src={product.image}
          alt={product.title}
          className="w-full h-40 sm:h-44 md:h-48 object-cover"
        />
        <div className="p-4 sm:p-5 md:p-6 bg-white">
          <h4 className="font-semibold text-base sm:text-lg md:text-xl mb-2 sm:mb-3 min-h-[3rem] sm:min-h-[3.5rem] md:min-h-[4rem] line-clamp-2">
            {product.title}
          </h4>
          <p className="text-xs sm:text-sm md:text-base text-gray-600 leading-relaxed min-h-[4rem] sm:min-h-[4.5rem] md:min-h-[5rem] line-clamp-3">
            {product.desc}
          </p>
          <a
            href="#"
            className="text-xs sm:text-sm md:text-base text-[var(--primary)] font-medium mt-3 sm:mt-4 inline-block hover:underline transition-colors duration-200"
          >
            Read More
          </a>
        </div>
      </div>
    );
  };

  return (
    <section className="relative z-0">
      {/* Top Section with Overlay */}
      <div
        className="relative pt-12 pb-50 sm:pt-16 md:pt-20  px-4 sm:px-6 md:px-12 lg:px-20 text-white"
        style={{
          backgroundImage: `linear-gradient(rgba(28, 26, 48, 0.85), rgba(28, 26, 48, 0.85)), url(${bgImage})`,
          backgroundSize: "cover",
          backgroundPosition: "center",
        }}
      >
        <div className="max-w-screen-xl mx-auto">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6 lg:gap-8">
            <div className="flex-1">
              <p className="text-xs sm:text-sm uppercase text-red-500 tracking-wider font-medium mb-2">
                Featured Products
              </p>
              <h2 className="text-2xl  sm:text-3xl md:text-3xl lg:text-4xl font-bold leading-tight">
                High Quality Mining Equipment <br className="hidden sm:block" />{" "}
                For Many Applications.
              </h2>
            </div>
            <div className="flex-1 lg:max-w-2xl">
              <p className="text-sm sm:text-base md:text-md text-gray-300 leading-relaxed">
                Professional mining equipment for stone crushing, mining
                separating, ore beneficiation, drying, briquette making, sand
                making and more.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Cards Section - Carousel Layout */}
      <div className="relative z-10 -mt-38 pb-15">
        <div className="max-w-screen-xl mx-auto px-4 sm:px-6 md:px-12 lg:px-20">
          <Carousel
            items={products}
            autoScrollInterval={7000}
            showArrows={false}
            showDots={true}
            responsive={{
              lg: { itemsPerSlide: 4, breakpoint: 1024 },
              md: { itemsPerSlide: 2, breakpoint: 768 },
              sm: { itemsPerSlide: 1, breakpoint: 0 },
            }}
            renderItem={renderProductCard}
            containerClassName="relative overflow-hidden"
            dotClassName="flex justify-center mt-8 gap-2 sm:gap-3"
            activeDotClassName="bg-[var(--primary)] shadow-lg"
            inactiveDotClassName="bg-gray-400 hover:bg-gray-600"
          />
        </div>
      </div>
    </section>
  );
}
