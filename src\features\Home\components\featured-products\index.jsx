import product1 from "../../../../assets/images/factoryImg.jpg";
import product2 from "../../../../assets/images/factoryImg.jpg";
import product3 from "../../../../assets/images/factoryImg.jpg";
import product4 from "../../../../assets/images/factoryImg.jpg";
import bgImage from "../../../../assets/images/factoryImg.jpg";
import { useState, useEffect, useCallback } from "react";

const products = [
  {
    image: product1,
    title: "Rotary Dryer",
    desc: "Rotary Dryer is also called Single drum Dryer or Single Cylinder Dryer, which is a kind of directly heated continuous rotating cylinder drying equipment....",
  },
  {
    image: product2,
    title: "Heavy Hammer Crushers",
    desc: "The heavy hammer crusher is commonly used for crushing materials with compressive strength....",
  },
  {
    image: product3,
    title: "Ball Mill",
    desc: "Ball mill is a common grinding equipment, but also an important core equipment in mineral processing operations. ....",
  },
  {
    image: product4,
    title: "Box Crushers",
    desc: "Box crusher aka Square box crusher has the characteristics of simple structure, easy maintenance, low price and high cost performance....",
  },
  {
    image: product1,
    title: "Cone Crushers",
    desc: "Cone crushers are compression machines that take in raw materials and reduce them in size by crushing them between a moving piece of steel and a stationary piece of steel....",
  },
  {
    image: product2,
    title: "Vibrating Screen",
    desc: "Vibrating screens are used to separate materials into various sizes for further processing or for end use. Depending on your needs....",
  },
  {
    image: product3,
    title: "Sand Washing Machine",
    desc: "Sand washing machine is used to remove the dust in sand. It aims at improving the quality of sand. The materials include the required gravel in building-site....",
  },
  {
    image: product4,
    title: "Jaw Crushers",
    desc: "Jaw crushers are typically used as primary crushers, or the first step in the process of reducing rock. They typically crush using compression....",
  },
];

export default function FeaturedProducts() {
  const [current, setCurrent] = useState(1); // Start at 1 (first real slide)
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [screenSize, setScreenSize] = useState("lg");

  // Detect screen size
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth >= 1024) {
        setScreenSize("lg"); // 4 cards per slide
      } else if (window.innerWidth >= 768) {
        setScreenSize("md"); // 2 cards per slide
      } else {
        setScreenSize("sm"); // 1 card per slide
      }
    };

    handleResize(); // Set initial size
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  // Group products based on screen size
  const getCardsPerSlide = () => {
    switch (screenSize) {
      case "lg":
        return 4;
      case "md":
        return 2;
      default:
        return 1;
    }
  };

  const cardsPerSlide = getCardsPerSlide();
  const groupedProducts = [];
  for (let i = 0; i < products.length; i += cardsPerSlide) {
    groupedProducts.push(products.slice(i, i + cardsPerSlide));
  }

  // Create extended slides array with duplicates for infinite effect
  const extendedSlides = [
    groupedProducts[groupedProducts.length - 1],
    ...groupedProducts,
    groupedProducts[0],
  ];

  // Reset current when screen size changes
  useEffect(() => {
    setCurrent(1);
  }, [screenSize]);

  const handleTransitionEnd = () => {
    setIsTransitioning(false);
    if (current === 0) {
      // If we're at the duplicate last slide, jump to real last slide
      setCurrent(groupedProducts.length);
    } else if (current === extendedSlides.length - 1) {
      // If we're at the duplicate first slide, jump to real first slide
      setCurrent(1);
    }
  };

  const nextSlide = useCallback(() => {
    if (isTransitioning) return;
    setIsTransitioning(true);
    setCurrent((prev) => prev + 1);
  }, [isTransitioning]);

  // Auto-scroll every 5 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      nextSlide();
    }, 7000);
    return () => clearInterval(interval);
  }, [nextSlide]);

  const goToSlide = (index) => {
    if (isTransitioning) return;
    setIsTransitioning(true);
    setCurrent(index + 1); // +1 because of the duplicate slide at the beginning
  };

  return (
    <section className="relative z-0">
      {/* Top Section with Overlay */}
      <div
        className="relative pt-12 pb-50 sm:pt-16 md:pt-20  px-4 sm:px-6 md:px-12 lg:px-20 text-white"
        style={{
          backgroundImage: `linear-gradient(rgba(28, 26, 48, 0.85), rgba(28, 26, 48, 0.85)), url(${bgImage})`,
          backgroundSize: "cover",
          backgroundPosition: "center",
        }}
      >
        <div className="max-w-screen-xl mx-auto">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6 lg:gap-8">
            <div className="flex-1">
              <p className="text-xs sm:text-sm uppercase text-red-500 tracking-wider font-medium mb-2">
                Featured Products
              </p>
              <h2 className="text-2xl  sm:text-3xl md:text-3xl lg:text-4xl font-bold leading-tight">
                High Quality Mining Equipment <br className="hidden sm:block" />{" "}
                For Many Applications.
              </h2>
            </div>
            <div className="flex-1 lg:max-w-2xl">
              <p className="text-sm sm:text-base md:text-md text-gray-300 leading-relaxed">
                Professional mining equipment for stone crushing, mining
                separating, ore beneficiation, drying, briquette making, sand
                making and more.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Cards Section - Carousel Layout */}
      <div className="relative z-10 -mt-38 pb-15">
        <div className="max-w-screen-xl mx-auto px-4 sm:px-6 md:px-12 lg:px-20">
          {/* Carousel Container */}
          <div className="relative overflow-hidden">
            <div
              className={`flex ${
                isTransitioning
                  ? "transition-transform duration-700 ease-in-out"
                  : ""
              }`}
              style={{ transform: `translateX(-${current * 100}%)` }}
              onTransitionEnd={handleTransitionEnd}
            >
              {extendedSlides.map((slideProducts, slideIndex) => (
                <div key={slideIndex} className="w-full flex-shrink-0">
                  <div
                    className={`${
                      cardsPerSlide === 1
                        ? "flex justify-center"
                        : `grid gap-4 sm:gap-6 ${
                            cardsPerSlide === 2 ? "grid-cols-2" : "grid-cols-4"
                          }`
                    }`}
                  >
                    {slideProducts.map((product, productIndex) => (
                      <div
                        key={productIndex}
                        className={`bg-white text-black rounded-xl shadow-md overflow-hidden hover:shadow-lg transition-all duration-300 ${
                          cardsPerSlide === 1 ? "w-full max-w-sm mx-auto" : ""
                        }`}
                      >
                        <img
                          src={product.image}
                          alt={product.title}
                          className="w-full  h-40 sm:h-44  md:h-48 object-cover"
                        />
                        <div className="p-4 sm:p-5 md:p-6 bg-white">
                          <h4 className="font-semibold text-base sm:text-lg md:text-xl mb-2 sm:mb-3 min-h-[3rem] sm:min-h-[3.5rem] md:min-h-[4rem] line-clamp-2">
                            {product.title}
                          </h4>
                          <p className="text-xs sm:text-sm md:text-base text-gray-600 leading-relaxed min-h-[4rem] sm:min-h-[4.5rem] md:min-h-[5rem] line-clamp-3">
                            {product.desc}
                          </p>
                          <a
                            href="#"
                            className="text-xs sm:text-sm md:text-base text-[var(--primary)] font-medium mt-3 sm:mt-4 inline-block hover:underline transition-colors duration-200"
                          >
                            Read More
                          </a>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>

            {/* Dots Navigation */}
            <div className="flex justify-center mt-8 gap-2 sm:gap-3">
              {groupedProducts.map((_, i) => (
                <button
                  key={i}
                  onClick={() => goToSlide(i)}
                  className={`w-2 h-1 md:w-3 md:h-1.5 lg:w-4 lg:h-2 rounded-full transition-all duration-300 hover:scale-125 ${
                    i ===
                    (current - 1 + groupedProducts.length) %
                      groupedProducts.length
                      ? "bg-[var(--primary)] shadow-lg"
                      : "bg-gray-400 hover:bg-gray-600"
                  }`}
                />
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
