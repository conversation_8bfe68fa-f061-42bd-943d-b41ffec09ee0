@import "tailwindcss";
@plugin "daisyui";

:root {
  --navbar-height: 4rem; /* Adjust this value to match your actual navbar height */
  --primary: #ce161b; /* Primary brand color */
}

/* Custom Swiper Pagination Styles */
.hero-swiper-pagination .swiper-pagination-bullet {
  width: 8px;
  height: 4px;
  border-radius: 9999px;
  background: rgba(255, 255, 255, 0.4);
  opacity: 1;
  transition: all 0.3s ease;
}

.hero-swiper-pagination .swiper-pagination-bullet:hover {
  background: rgba(255, 255, 255, 0.6);
  transform: scale(1.25);
}

.hero-swiper-pagination .swiper-pagination-bullet-active {
  background: white;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

@media (min-width: 768px) {
  .hero-swiper-pagination .swiper-pagination-bullet {
    width: 12px;
    height: 6px;
  }
}

@media (min-width: 1024px) {
  .hero-swiper-pagination .swiper-pagination-bullet {
    width: 16px;
    height: 8px;
  }
}

.products-swiper-pagination .swiper-pagination-bullet {
  width: 8px;
  height: 4px;
  border-radius: 9999px;
  background: #9ca3af;
  opacity: 1;
  transition: all 0.3s ease;
}

.products-swiper-pagination .swiper-pagination-bullet:hover {
  background: #6b7280;
  transform: scale(1.25);
}

.products-swiper-pagination .swiper-pagination-bullet-active {
  background: var(--primary);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

@media (min-width: 768px) {
  .products-swiper-pagination .swiper-pagination-bullet {
    width: 12px;
    height: 6px;
  }
}

@media (min-width: 1024px) {
  .products-swiper-pagination .swiper-pagination-bullet {
    width: 16px;
    height: 8px;
  }
}

/* Set Poppins as the default font family for the entire project */
* {
  font-family: "Poppins", sans-serif;
}

body {
  font-family: "Poppins", sans-serif;
}

/* Line clamp utilities for text truncation */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
